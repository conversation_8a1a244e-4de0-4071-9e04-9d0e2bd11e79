name: <PERSON><PERSON><PERSON><PERSON> - Windows Build

on:
  push:
    paths:
      - "nuget.config"
      - ".github/workflows/dflasher-server-master.yml"
      - "source/TurboCraft.DFlasher/**"
      - "source/TurboCraft.DFlasher.App/**"
      - "source/TurboCraft.DFlasher.Assets*/**"
      - "source/TurboCraft.DFlasher.Server.Shared/**"
      - "source/TurboCraft.Flasher*/**"
      - "source/TurboCraft.UI.Core/**"
      - "source/TurboCraft.UCL*/**"
    branches: [ "master" ]
  pull_request:
    paths:
      - "nuget.config"
      - ".github/workflows/dflasher-server-master.yml"
      - "source/TurboCraft.DFlasher/**"
      - "source/TurboCraft.DFlasher.App/**"
      - "source/TurboCraft.DFlasher.Assets*/**"
      - "source/TurboCraft.DFlasher.Server.Shared/**"
      - "source/TurboCraft.Flasher*/**"
      - "source/TurboCraft.UI.Core/**"
      - "source/TurboCraft.UCL*/**"
  workflow_dispatch:

permissions:
  checks: write
  contents: write
  pull-requests: write
  packages: write

jobs:
  build:
    permissions:
      checks: write
      contents: write
      pull-requests: write
      packages: write
    runs-on: windows-self-hosted
    timeout-minutes: 60

    steps:
    - name: Checkout repository
      uses: actions/checkout@v3

    - name: Update nuget.config local feed path
      run: |
        sed -i "s|./distribution-files/nuget|${{ github.workspace }}\\distribution-files\\nuget|g" nuget.config

    - uses: weslleymurdock/setup-maui-action@v1.1
      with:
        dotnet-version: 9.0.x

    - name: workload api
      run: dotnet workload restore

    - name: Update version in AppxManifest
      run: |
        $csprojPath = "source\TurboCraft.DFlasher.App\TurboCraft.DFlasher.App.csproj"
        $appxmanifestPath = "source\TurboCraft.DFlasher.App\Platforms\Windows\Package.appxmanifest"
        $installerPath = "distribution-files\installer\dflasher-installer.iss"

        # Load the .csproj file and extract the version
        [xml]$csprojXml = Get-Content $csprojPath
        $version = $csprojXml.Project.PropertyGroup.ApplicationDisplayVersion
        $version = [system.String]::Join(" ", $version)
        $version = $version.replace(' ','') + ".0.${{ github.run_number }}.0"

        # Update the AppxManifest file
        [xml]$manifestXml = Get-Content $appxmanifestPath
        $manifestXml.Package.Identity.Version = $version
        $manifestXml.Save($appxmanifestPath)
        Write-Host "Updated Package.appxmanifest Version to $version"

        # Update the installer script
        $installerContent = Get-Content $installerPath
        $installerContent = $installerContent -replace '(#define MyAppDisplayVersion\s+)"\d+(\.\d+)*"', "`$1`"$version`""
        Set-Content $installerPath -Value $installerContent
        Write-Host $installerContent

    - name: Publish .NET project
      run: dotnet publish source/TurboCraft.DFlasher.App/TurboCraft.DFlasher.App.csproj -c Release -f net9.0-windows10.0.19041.0 /p:ApplicationVersion=${{ github.run_number }} /p:SentryProject=dflasher-app-windows --output "${{ runner.temp }}\dflasher-app-publish"

    - name: Move msix package
      run: |
        $msixPath = Get-ChildItem "${{ github.workspace }}\source\TurboCraft.DFlasher.App\bin" -Recurse -Filter "*.msix" | Select-Object -First 1
        if ($msixPath -eq $null) {
            Write-Host "No msix package found"
            exit 1
        }
        $msixPath = $msixPath.FullName
        Move-Item $msixPath "${{ runner.temp }}\TurboCraft.DFlasher.App.msix" -Force
        Write-Host "Moving msix package to ${{ runner.temp }}\TurboCraft.DFlasher.App.msix"

    - name: Sign MSIX package
      uses: dlemstra/code-sign-action@v1
      with:
        certificate: '${{ secrets.WINDOWSDFLASHERPFXB64 }}'
        folder: ${{ runner.temp }}
        recursive: false
        files: TurboCraft.DFlasher.App.msix
        password: dflasher

    - name: Create installer
      shell: pwsh
      run: |
        # Define paths
        $7zPath = "${{ github.workspace }}\distribution-files\installer\innosetup.zip"
        $extractPath = "${{ github.workspace }}\distribution-files\installer"
        $isccPath = "${{ github.workspace }}\distribution-files\installer\innosetup\app\ISCC.exe"
        # Unzip innosetup.7z using System.IO.Compression.ZipFile
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::ExtractToDirectory($7zPath, $extractPath)

        # Copy the installer script to the temp directory
        Copy-Item "${{ github.workspace }}/distribution-files/installer/dflasher-installer.iss" -Destination "${{ runner.temp }}/dflasher-installer.iss"
        Copy-Item "${{ github.workspace }}/dflasher-changelog.md" -Destination "${{ runner.temp }}/dflasher-changelog.md"

        # Run the Inno Setup Compiler
        Push-Location ${{ runner.temp }}
        & "$isccPath" "${{ runner.temp }}\dflasher-installer.iss"

    - name: Set app version variables
      id: vars
      shell: powershell
      run: |
        $csprojPath = "${{ github.workspace }}\source\TurboCraft.DFlasher.App\TurboCraft.DFlasher.App.csproj"
        [xml]$csprojXml = Get-Content $csprojPath
        $version = $csprojXml.Project.PropertyGroup.ApplicationDisplayVersion
        $version = [system.String]::Join("", $version).Trim()
        $buildId = "${{ github.run_number }}"
        echo "::set-output name=version::$version"
        echo "::set-output name=buildId::$buildId"

    - name: List all files from ${{ github.workspace }}
      run: |
        echo "Listing all files and directories recursively in ${{ github.workspace }}:"
        Get-ChildItem -Path "${{ github.workspace }}" -Recurse -File | ForEach-Object { Write-Host "File: $($_.FullName)" }

    - name: List all files from ${{ runner.temp }}
      run: |
        echo "Listing all files and directories recursively in ${{ runner.temp }}:"
        Get-ChildItem -Path "${{ runner.temp }}" -Recurse -File | ForEach-Object { Write-Host "File: $($_.FullName)" }

    - name: Create GitHub Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: dflasher-win-v${{ steps.vars.outputs.version }}b${{ steps.vars.outputs.buildId }}
        release_name: dflasher-win-v${{ steps.vars.outputs.version }}b${{ steps.vars.outputs.buildId }}
        body_path: ${{ github.workspace }}\dflasher-changelog.md
        draft: false
        prerelease: false

    - name: Upload Msix to Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ${{ runner.temp }}/TurboCraft.DFlasher.App.msix
        asset_name: DFlasher-v${{ steps.vars.outputs.version }}b${{ steps.vars.outputs.buildId }}.msix
        asset_content_type: application/vnd.ms-msix

    - name: Upload artifacts to DFlasher backend
      shell: pwsh
      run: |
        # Define variables
        $UPLOAD_URL = "https://backend.net.mgflasher.com/api/v1/distribution/external/manage/dflasher/Windows/upload"
        $API_KEY = "${{ secrets.MGFLASHER_BACKEND_NET_API_KEY }}"

        # Find the installer file
        $FILE = "${{ runner.temp }}/TurboCraft.DFlasher.App.msix"
        if (-not (Test-Path $FILE)) {
          Write-Error "No .exe file found at $FILE"
          exit 1
        }
        $NEW_FILE_NAME = "DFlasher-v${{ steps.vars.outputs.version }}b${{ steps.vars.outputs.buildId }}.msix"
        Rename-Item -Path $FILE -NewName $NEW_FILE_NAME -Force
        $FILE = "${{ runner.temp }}/$NEW_FILE_NAME"

        Write-Host "Uploading file: $FILE"

        # Upload the file using curl
        $headers = @{
          "ApiKey" = $API_KEY
        }

        # Use curl to upload the file
        curl.exe -X POST `
          -H "ApiKey: $API_KEY" `
          -F "file=@$FILE" `
          $UPLOAD_URL
