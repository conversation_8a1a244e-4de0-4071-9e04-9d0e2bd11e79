﻿using System.Text.RegularExpressions;

if (args.Length == 0)
{
    Console.WriteLine("Usage: changelog-compiler <input-file>");
    Console.WriteLine("Output will be saved as <input-file>.compiled.md");
    Environment.Exit(1);
}

var input = args[0];
var output = input.Replace(".md", "") + ".compiled.md";

if (!File.Exists(input))
{
    Console.WriteLine($"Error: Input file '{input}' not found.");
    Environment.Exit(1);
}

var skipLineRegex = new Regex(@"\[Internal](.*)$", RegexOptions.Compiled);
var versionRegex = new Regex(@"^## \[\d{2,3}\]$", RegexOptions.Compiled);

try
{
    var file = await File.ReadAllLinesAsync(input);
    var lastVersionLines = new List<string>();
    var lastVersionMatched = false;

    foreach (var line in file)
    {
        if (versionRegex.IsMatch(line) && !lastVersionMatched)
        {
            lastVersionMatched = true;
            continue;
        }
        if (versionRegex.IsMatch(line))
        {
            break;
        }
        if(skipLineRegex.IsMatch(line))
        {
            continue;
        }
        var text = line.Replace("## ", "");
        lastVersionLines.Add(text);
    }

    var filtered = lastVersionLines
        .Select(x => x.Trim())
        .Where(x => !string.IsNullOrEmpty(x))
        .Skip(4); //preambule formula skip

    var joined = string.Join("\n", filtered);

    Console.WriteLine(joined);
    await File.WriteAllTextAsync(output, joined);

    Console.WriteLine($"Successfully compiled changelog to: {output}");
}
catch (Exception ex)
{
    Console.WriteLine($"Error processing file: {ex.Message}");
    Environment.Exit(1);
}