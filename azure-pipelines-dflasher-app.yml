trigger:
  branches:
    include:
    - master
  paths:
    include:
    - "nuget.config"
    - "azure-pipelines-dflasher-app.yml"
    - "source/TurboCraft.DFlasher/**"
    - "source/TurboCraft.DFlasher.App/**"
    - "source/TurboCraft.DFlasher.Assets*/**"
    - "source/TurboCraft.DFlasher.Server.Shared/**"
    - "source/TurboCraft.Flasher*/**"
    - "source/TurboCraft.UI.Core/**"
    - "source/TurboCraft.UCL*/**"

pr:
  autoCancel: true
  branches:
    include:
    - master
  paths:
    include:
    - "nuget.config"
    - "azure-pipelines-dflasher-app.yml"
    - "source/TurboCraft.DFlasher/**"
    - "source/TurboCraft.DFlasher.App/**"
    - "source/TurboCraft.DFlasher.Assets*/**"
    - "source/TurboCraft.DFlasher.Server.Shared/**"
    - "source/TurboCraft.Flasher*/**"
    - "source/TurboCraft.UI.Core/**"
    - "source/TurboCraft.UCL*/**"

parameters:
- name: platform
  type: string
  default: 'all'
  values:
  - all
  - android
  - ios

jobs:
- job: BuildApps
  pool:
    vmImage: 'macOS-15'

  steps:
  - task: Bash@3
    displayName: Prepare Git Repo
    inputs:
      targetType: 'inline'
      script: |
        git config --list --show-origin
        git config --global gc.auto 256
        git config --local gc.auto 256
        git prune
        git gc --auto

  - task: CmdLine@2
    displayName: 'Set Xcode v16.3.0'
    condition: eq(variables['Agent.OS'], 'Darwin')
    inputs:
      script: echo '##vso[task.setvariable variable=MD_APPLE_SDK_ROOT;]'/Applications/Xcode_16.3.app;sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer

  - task: CmdLine@2
    displayName: 'Switch to Xcode 16.3'
    condition: eq(variables['Agent.OS'], 'Darwin')
    inputs:
      script: sudo xcode-select -switch /Applications/Xcode_16.3.app/Contents/Developer

  - task: Bash@3
    displayName: "Updating aps environment"
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    inputs:
      targetType: 'inline'
      script: |
        echo "Updating aps environment in Entitlements.plist"
        sed -i '' 's/development/production/' source/TurboCraft.DFlasher.App/Platforms/iOS/Entitlements.plist
        echo "Entitlements.plist"
        cat source/TurboCraft.DFlasher.App/Platforms/iOS/Entitlements.plist

  - task: Bash@3
    displayName: Prepare nuget.config
    inputs:
      targetType: 'inline'
      script: sed -i '' 's/distribution-files\/nuget/.\/..\/s\/distribution-files\/nuget/' nuget.config

  - task: InstallAppleCertificate@2
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    inputs:
      certSecureFile: CertificatesDFlasher.p12
      certPwd: dflasher
      keychain: 'temp'
      opensslPkcsArgs: '-legacy'
    continueOnError: true

  - task: InstallAppleCertificate@2
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    inputs:
      certSecureFile: CertificatesDFlasher.p12
      certPwd: dflasher
      keychain: 'temp'

  - task: InstallAppleProvisioningProfile@1
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    inputs:
      provisioningProfileLocation: secureFiles
      provProfileSecureFile: DflasherDistProvisioningProfile.mobileprovision

  - task: NuGetToolInstaller@1
    inputs:
      versionSpec: '6.6.1'

  - task: UseDotNet@2
    displayName: 'Use .NET Core 9 sdk'
    inputs:
      packageType: sdk
      version: 9.0.x
      installationPath: $(Agent.ToolsDirectory)/dotnet

  - task: JavaToolInstaller@0
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'android'))
    displayName: Java 11
    inputs:
      versionSpec: '11'
      jdkArchitectureOption: 'x64'
      jdkSourceOption: 'PreInstalled'

  - bash: |
      echo "##vso[task.setvariable variable=JI_JAVA_HOME]$(JAVA_HOME_11_X64)"
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'android'))
    displayName: 'Setup JDK Paths'

  - task: CmdLine@2
    displayName: dotnet workload install
    inputs:
      script: 'dotnet workload install maui-ios maui-android maui-windows maui-maccatalyst'

  - task: DotNetCoreCLI@2
    displayName: DFlasher - dotnet restore
    inputs:
      command: 'restore'
      projects: 'source/TurboCraft.DFlasher.App/TurboCraft.DFlasher.App.csproj'
      feedsToUse: 'config'
      nugetConfigPath: 'nuget.config'

  - task: DotNetCoreCLI@2
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    displayName: DFlasher - dotnet build iOS
    inputs:
      command: 'build'
      projects: 'source/TurboCraft.DFlasher.App/TurboCraft.DFlasher.App.csproj'
      arguments: '-c Release -f net9.0-ios -r ios-arm64 /p:BuildIpa=true /p:ArchiveOnBuild=true --no-incremental /p:ApplicationVersion=$(Build.BuildId) /p:SentryProject=dflasher-app-ios'

  - task: DotNetCoreCLI@2
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'android'))
    displayName: DFlasher - dotnet build aab
    inputs:
      command: 'build'
      projects: 'source/TurboCraft.DFlasher.App/TurboCraft.DFlasher.App.csproj'
      arguments: '-c Release -f net9.0-android /p:AndroidPackageFormat=aab /p:ApplicationVersion=$(Build.BuildId) /p:SentryProject=dflasher-app-android'



  - task: CopyFiles@2
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'android'))
    displayName: 'Copy Android binaries'
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)'
      Contents: '**/*-Signed.a*'
      TargetFolder: '$(Build.ArtifactStagingDirectory)/android'
      flattenFolders: true

  - task: CopyFiles@2
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    displayName: 'Copy iOS binaries'
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)'
      Contents: '**/*.ipa'
      TargetFolder: '$(Build.ArtifactStagingDirectory)/ios'
      flattenFolders: true
      OverWrite: true

  - task: Bash@3
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'android'))
    displayName: 'Duplicate Android APK'
    inputs:
      targetType: 'inline'
      script: |
        version=$(grep '<ApplicationDisplayVersion>' "$(Build.SourcesDirectory)/source/TurboCraft.DFlasher.App/TurboCraft.DFlasher.App.csproj" | sed -E 's/.*<ApplicationDisplayVersion>([^<]+)<.*/\1/')
        buildId="$(Build.BuildId)"
        cd "$(Build.ArtifactStagingDirectory)/android"
        if [ -f "com.x2859910ontarioinc.dflasher-Signed.apk" ]; then
          cp "com.x2859910ontarioinc.dflasher-Signed.apk" "com.x2859910ontarioinc.dflasher-Signed-v${version}b${buildId}.apk"
        fi

  - task: Bash@3
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    displayName: 'Duplicate iOS IPA'
    inputs:
      targetType: 'inline'
      script: |
        version=$(grep '<ApplicationDisplayVersion>' "$(Build.SourcesDirectory)/source/TurboCraft.DFlasher.App/TurboCraft.DFlasher.App.csproj" | sed -E 's/.*<ApplicationDisplayVersion>([^<]+)<.*/\1/')
        buildId="$(Build.BuildId)"
        cd "$(Build.ArtifactStagingDirectory)/ios"
        if [ -f "TurboCraft.DFlasher.App.ipa" ]; then
          cp "TurboCraft.DFlasher.App.ipa" "DFlasher-v${version}b${buildId}.ipa"
        fi

  - task: PublishBuildArtifacts@1
    displayName: 'Publish Artifacts'
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)'
      ArtifactName: 'drop'
      publishLocation: 'Container'