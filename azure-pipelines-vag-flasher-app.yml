trigger:
  branches:
    include:
    - master
  paths:
    include:
    - "nuget.config"
    - "azure-pipelines-vag-flasher-app.yml"
    - "source/TurboCraft.VagFlasher.App/**"
    - "source/TurboCraft.VagFlasher.Encryption*/**"
    - "source/TurboCraft.VagFlasher.Flasher/**"
    - "source/TurboCraft.VagFlasher.Logger/**"
    - "source/TurboCraft.Flasher*/**"
    - "source/TurboCraft.UI.Core/**"
    - "source/TurboCraft.UCL*/**"

pr:
  autoCancel: true
  branches:
    include:
    - master
  paths:
    include:
    - "nuget.config"
    - "azure-pipelines-vag-flasher-app.yml"
    - "source/TurboCraft.VagFlasher.App/**"
    - "source/TurboCraft.VagFlasher.Encryption*/**"
    - "source/TurboCraft.VagFlasher.Flasher/**"
    - "source/TurboCraft.VagFlasher.Logger/**"
    - "source/TurboCraft.Flasher*/**"
    - "source/TurboCraft.UI.Core/**"
    - "source/TurboCraft.UCL*/**"

parameters:
- name: platform
  type: string
  default: 'all'
  values:
  - all
  - android
  - ios

jobs:
- job: BuildApps
  pool:
    vmImage: 'macOS-15'

  steps:
  - task: Bash@3
    displayName: Prepare Git Repo
    inputs:
      targetType: 'inline'
      script: |
        git config --list --show-origin
        git config --global gc.auto 256
        git config --local gc.auto 256
        git prune
        git gc --auto

  - task: CmdLine@2
    displayName: 'Set Xcode v16.2.0'
    condition: eq(variables['Agent.OS'], 'Darwin')
    inputs:
      script: echo '##vso[task.setvariable variable=MD_APPLE_SDK_ROOT;]'/Applications/Xcode_16.2.app;sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer

  - task: CmdLine@2
    displayName: 'Switch to Xcode 16.2'
    condition: eq(variables['Agent.OS'], 'Darwin')
    inputs:
      script: sudo xcode-select -switch /Applications/Xcode_16.2.app/Contents/Developer

  - task: Bash@3
    displayName: "Updating aps environment"
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    inputs:
      targetType: 'inline'
      script: |
        echo "Updating aps environment in Entitlements.plist"
        sed -i '' 's/development/production/' source/TurboCraft.VagFlasher.App/Platforms/iOS/Entitlements.plist
        echo "Entitlements.plist"
        cat source/TurboCraft.VagFlasher.App/Platforms/iOS/Entitlements.plist

  - task: Bash@3
    displayName: Prepare nuget.config
    inputs:
      targetType: 'inline'
      script: sed -i '' 's/distribution-files\/nuget/.\/..\/s\/distribution-files\/nuget/' nuget.config

  - task: InstallAppleCertificate@2
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    inputs:
      certSecureFile: CertificatesVagFlasher.p12
      certPwd: vagflasher
      keychain: 'temp'
      opensslPkcsArgs: '-legacy'
    continueOnError: true

  - task: InstallAppleCertificate@2
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    inputs:
      certSecureFile: CertificatesVagFlasher.p12
      certPwd: vagflasher
      keychain: 'temp'

  - task: InstallAppleProvisioningProfile@1
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    inputs:
      provisioningProfileLocation: secureFiles
      provProfileSecureFile: VagFlasherDistProvisioningProfile.mobileprovision

  - task: NuGetToolInstaller@1
    inputs:
      versionSpec: '6.6.1'

  - task: UseDotNet@2
    displayName: 'Use .NET Core 9 sdk'
    inputs:
      packageType: sdk
      version: 9.0.203
      installationPath: $(Agent.ToolsDirectory)/dotnet

  - task: JavaToolInstaller@0
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'android'))
    displayName: Java 11
    inputs:
      versionSpec: '11'
      jdkArchitectureOption: 'x64'
      jdkSourceOption: 'PreInstalled'

  - bash: |
      echo "##vso[task.setvariable variable=JI_JAVA_HOME]$(JAVA_HOME_11_X64)"
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'android'))
    displayName: 'Setup JDK Paths'

  - task: CmdLine@2
    displayName: dotnet workload install
    inputs:
      script: 'dotnet workload install maui-ios maui-android maui-windows maui-maccatalyst --version 9.0.202'

  - task: DotNetCoreCLI@2
    displayName: VAG Flasher - dotnet restore
    inputs:
      command: 'restore'
      projects: 'source/TurboCraft.VagFlasher.App/TurboCraft.VagFlasher.App.csproj'
      feedsToUse: 'config'
      nugetConfigPath: 'nuget.config'

  - task: DotNetCoreCLI@2
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'android'))
    displayName: VAG Flasher - dotnet build aab
    inputs:
      command: 'build'
      projects: 'source/TurboCraft.VagFlasher.App/TurboCraft.VagFlasher.App.csproj'
      arguments: '-c Release -f net9.0-android /p:AndroidPackageFormat=aab /p:ApplicationVersion=$(Build.BuildId) /p:SentryProject=dflasher-app-android'

  - task: DotNetCoreCLI@2
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    displayName: VAG Flasher - dotnet build iOS
    inputs:
      command: 'build'
      projects: 'source/TurboCraft.VagFlasher.App/TurboCraft.VagFlasher.App.csproj'
      arguments: '-c Release -f net9.0-ios -r ios-arm64 /p:BuildIpa=true /p:ArchiveOnBuild=true --no-incremental /p:ApplicationVersion=$(Build.BuildId) /p:SentryProject=vag-flasher-app-ios'

  - task: CopyFiles@2
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'android'))
    displayName: 'Copy Android binaries'
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)'
      Contents: '**/*-Signed.a*'
      TargetFolder: '$(Build.ArtifactStagingDirectory)/android'
      flattenFolders: true

  - task: CopyFiles@2
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    displayName: 'Copy iOS binaries'
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)'
      Contents: '**/*.ipa'
      TargetFolder: '$(Build.ArtifactStagingDirectory)/ios'
      flattenFolders: true
      OverWrite: true

  - task: Bash@3
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'android'))
    displayName: 'Duplicate Android APK'
    inputs:
      targetType: 'inline'
      script: |
        version=$(grep '<ApplicationDisplayVersion>' "$(Build.SourcesDirectory)/source/TurboCraft.VagFlasher.App/TurboCraft.VagFlasher.App.csproj" | sed -E 's/.*<ApplicationDisplayVersion>([^<]+)<.*/\1/')
        buildId="$(Build.BuildId)"
        cd "$(Build.ArtifactStagingDirectory)/android"
        if [ -f "com.vag.flasher.app-Signed.apk" ]; then
          cp "com.vag.flasher.app-Signed.apk" "com.vag.flasher.app-Signed-v${version}b${buildId}.apk"
        fi

  - task: Bash@3
    condition: or(eq('${{ parameters.platform }}', 'all'), eq('${{ parameters.platform }}', 'ios'))
    displayName: 'Duplicate iOS IPA'
    inputs:
      targetType: 'inline'
      script: |
        version=$(grep '<ApplicationDisplayVersion>' "$(Build.SourcesDirectory)/source/TurboCraft.VagFlasher.App/TurboCraft.VagFlasher.App.csproj" | sed -E 's/.*<ApplicationDisplayVersion>([^<]+)<.*/\1/')
        buildId="$(Build.BuildId)"
        cd "$(Build.ArtifactStagingDirectory)/ios"
        if [ -f "TurboCraft.VagFlasher.App.ipa" ]; then
          cp "TurboCraft.VagFlasher.App.ipa" "VagFlasher-v${version}b${buildId}.ipa"
        fi

  - task: PublishBuildArtifacts@1
    displayName: 'Publish Artifacts'
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)'
      ArtifactName: 'drop'
      publishLocation: 'Container'