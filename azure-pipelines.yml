trigger:
  branches:
    include:
    - master

pr:
  autoCancel: true
  branches:
    include:
    - master

jobs:
- job: BuildApp
  pool:
    vmImage: 'macOS-15'
  steps:
  - task: Bash@3
    displayName: Prepare Git Repo
    inputs:
      targetType: 'inline'
      script: |
        git config --list --show-origin
        git config --global gc.auto 256
        git config --local gc.auto 256
        git prune
        git gc --auto

  - task: Bash@3
    displayName: Download App Files
    inputs:
      targetType: 'inline'
      script: |
        # Create directory if it doesn't exist
        mkdir -p "MgFlasher.Flasher.Services/AppFiles/Assets"

        # List files before download (to show what's being updated)
        echo "Files in directory before download:"
        if [ -d "MgFlasher.Flasher.Services/AppFiles/Assets" ]; then
          ls -la "MgFlasher.Flasher.Services/AppFiles/Assets/"
        else
          echo "Directory does not exist yet"
        fi

        # Download logger parameters definitions
        echo "Downloading logger_param_definitions.xlsx..."
        curl -o "MgFlasher.Flasher.Services/AppFiles/Assets/logger_param_definitions.xlsx" "https://backend.mgflasher.com/app/api/appfiles/LoggerParamsDefinitions" --create-dirs -f

        # Download flashing options definitions
        echo "Downloading flashing_option_definitions.xlsx..."
        curl -o "MgFlasher.Flasher.Services/AppFiles/Assets/flashing_option_definitions.xlsx" "https://backend.mgflasher.com/app/api/appfiles/FlashingOptionsDefinitions" --create-dirs -f

        # List files after download with details
        echo "Files in directory after download:"
        echo "--------------------------------------------------------------"
        if [ "$(Agent.OS)" == "Darwin" ]; then
          # macOS format
          ls -la "MgFlasher.Flasher.Services/AppFiles/Assets/"
        else
          # Linux format
          ls -la "MgFlasher.Flasher.Services/AppFiles/Assets/"
        fi
        echo "--------------------------------------------------------------"

        # Verify file sizes
        logger_file_size=$(stat -f%z "MgFlasher.Flasher.Services/AppFiles/Assets/logger_param_definitions.xlsx" 2>/dev/null || stat -c%s "MgFlasher.Flasher.Services/AppFiles/Assets/logger_param_definitions.xlsx")
        flashing_file_size=$(stat -f%z "MgFlasher.Flasher.Services/AppFiles/Assets/flashing_option_definitions.xlsx" 2>/dev/null || stat -c%s "MgFlasher.Flasher.Services/AppFiles/Assets/flashing_option_definitions.xlsx")

        echo "File sizes:"
        echo "- logger_param_definitions.xlsx: ${logger_file_size} bytes"
        echo "- flashing_option_definitions.xlsx: ${flashing_file_size} bytes"

        # Check file sizes to ensure they're not empty
        if [ "$logger_file_size" -lt 100 ]; then
          echo "##vso[task.logissue type=error]Downloaded logger_param_definitions.xlsx is too small (${logger_file_size} bytes). File may be corrupted."
          exit 1
        fi

        if [ "$flashing_file_size" -lt 100 ]; then
          echo "##vso[task.logissue type=error]Downloaded flashing_option_definitions.xlsx is too small (${flashing_file_size} bytes). File may be corrupted."
          exit 1
        fi

        echo "##vso[task.complete result=Succeeded]All app files successfully downloaded and verified in MgFlasher.Flasher.Services/AppFiles/Assets/"

  # iOS specific setup
  - task: CmdLine@2
    displayName: 'Set Xcode v16.2.0'
    condition: eq(variables['Agent.OS'], 'Darwin')
    inputs:
      script: echo '##vso[task.setvariable variable=MD_APPLE_SDK_ROOT;]'/Applications/Xcode_16.2.app;sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer

  - task: CmdLine@2
    displayName: 'Switch to Xcode 16.2'
    condition: eq(variables['Agent.OS'], 'Darwin')
    inputs:
      script: sudo xcode-select -switch /Applications/Xcode_16.2.app/Contents/Developer

  - task: Bash@3
    displayName: "Updating aps environment"
    inputs:
      targetType: 'inline'
      script: |
        echo "Updating aps environment in Entitlements.plist"
        sed -i '' 's/development/production/' MgFlasher/Platforms/iOS/Entitlements.plist
        echo "Entitlements.plist"
        cat MgFlasher/Platforms/iOS/Entitlements.plist

  - task: InstallAppleCertificate@2
    inputs:
      certSecureFile: Certificates.p12
      certPwd: mgflasher
      keychain: 'temp'
      opensslPkcsArgs: '-legacy'
    continueOnError: true

  - task: InstallAppleCertificate@2
    inputs:
      certSecureFile: Certificates.p12
      certPwd: mgflasher
      keychain: 'temp'

  - task: InstallAppleProvisioningProfile@1
    inputs:
      provisioningProfileLocation: secureFiles
      provProfileSecureFile: MGFlasherDistProvisioningProfile.mobileprovision

  # Android specific setup
  - task: Bash@3
    displayName: "Updating google services json"
    inputs:
      targetType: 'inline'
      script: |
        echo "Updating Google Json"
        echo "$GOOGLE_JSON" > MgFlasher/Platforms/Android/google-services.json
        sed -i -e 's/\\"/'\"'/g' MgFlasher/Platforms/Android/google-services.json
        echo "File content:"
        cat MgFlasher/Platforms/Android/google-services.json

  - task: JavaToolInstaller@0
    displayName: Java 11
    inputs:
      versionSpec: '11'
      jdkArchitectureOption: 'x64'
      jdkSourceOption: 'PreInstalled'

  - bash: |
      echo "##vso[task.setvariable variable=JI_JAVA_HOME]$(JAVA_HOME_11_X64)"
    displayName: 'Setup JDK Paths'

  # Common setup
  - task: Bash@3
    displayName: Prepare nuget.config
    inputs:
      targetType: 'inline'
      script: |
        if [ "$(Agent.OS)" == "Darwin" ]; then
          sed -i '' 's/distribution-files\/nuget/.\/..\/s\/distribution-files\/nuget/' nuget.config
        else
          sed -i 's/distribution-files\/nuget/.\/..\/s\/distribution-files\/nuget/' nuget.config
        fi

  - task: NuGetToolInstaller@1
    displayName: Install Nuget Tool
    inputs:
      versionSpec: '6.6.1'

  - task: UseDotNet@2
    displayName: 'Use .NET Core 6 sdk'
    inputs:
      packageType: sdk
      version: 6.0.x

  - task: UseDotNet@2
    displayName: 'Use .NET Core 8 sdk'
    inputs:
      packageType: sdk
      version: 8.0.x

  - task: UseDotNet@2
    displayName: 'Use .NET Core 9 sdk'
    inputs:
      packageType: sdk
      version: 9.0.203
      installationPath: $(Agent.ToolsDirectory)/dotnet

  - task: CmdLine@2
    displayName: dotnet workload install
    inputs:
      script: 'dotnet workload install maui-ios maui-android --version 9.0.202'

  - task: DotNetCoreCLI@2
    displayName: dotnet restore
    inputs:
      command: 'restore'
      projects: 'MgFlasher/MgFlasher.csproj'
      feedsToUse: 'config'
      nugetConfigPath: 'nuget.config'

  # iOS build
  - task: DotNetCoreCLI@2
    displayName: dotnet build iOS
    inputs:
      command: 'build'
      projects: 'MgFlasher/MgFlasher.csproj'
      arguments: '-c Release -f net9.0-ios -r ios-arm64 /p:BuildIpa=true /p:ArchiveOnBuild=true /p:EnableTestCloud=false --no-incremental /p:ApplicationVersion=$(Build.BuildId) /p:SentryProject=ios'

  # Android build
  - task: DotNetCoreCLI@2
    displayName: dotnet build Android
    inputs:
      command: 'build'
      projects: 'MgFlasher/MgFlasher.csproj'
      arguments: '-c Release -f net9.0-android /p:AndroidPackageFormat=aab /p:ApplicationVersion=$(Build.BuildId) /p:SentryProject=android'

  # Common post-build tasks
  - task: DotNetCoreCLI@2
    displayName: Compile ChangeLog.md
    inputs:
      command: 'run'
      projects: 'Tools/MgFlasher.Tools.Changelog/MgFlasher.Tools.Changelog.csproj'
      arguments: '-- ChangeLog.md'
      workingDirectory: '$(Build.SourcesDirectory)'

  - task: CopyFiles@2
    displayName: 'Copy changelog to: $(build.artifactstagingdirectory)'
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)'
      Contents: 'ChangeLog.compiled.md'
      TargetFolder: '$(Build.ArtifactStagingDirectory)'
      flattenFolders: true
      OverWrite: true

  - task: CopyFiles@2
    displayName: 'Copy tests to: $(build.artifactstagingdirectory)'
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)'
      Contents: 'MgFlasher.UI.E2E/**'
      TargetFolder: '$(Build.ArtifactStagingDirectory)/e2e'
      flattenFolders: false
      OverWrite: true

  # iOS artifacts
  - task: CopyFiles@2
    displayName: 'Copy iOS binary to: $(build.artifactstagingdirectory)/ios'
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)/MgFlasher/bin/Release/net9.0-ios/ios-arm64'
      Contents: '**/*.ipa'
      TargetFolder: '$(Build.ArtifactStagingDirectory)/ios'
      flattenFolders: true
      OverWrite: true

  - task: Bash@3
    displayName: 'Duplicate mgflasher.ipa'
    inputs:
      targetType: 'inline'
      script: |
        version=$(grep '<ApplicationDisplayVersion>' "$(Build.SourcesDirectory)/MgFlasher/MgFlasher.csproj" | sed -E 's/.*<ApplicationDisplayVersion>([^<]+)<.*/\1/')
        buildId="$(Build.BuildId)"
        cd "$(Build.ArtifactStagingDirectory)/ios"
        if [ -f "MgFlasher.ipa" ]; then
          cp "MgFlasher.ipa" "MgFlasher-v${version}b${buildId}.ipa"
        fi

  # Android artifacts
  - task: CopyFiles@2
    displayName: 'Copy Android binaries to: $(build.artifactstagingdirectory)/android'
    inputs:
      SourceFolder: '$(agent.builddirectory)'
      Contents: '**/*-Signed.a*'
      TargetFolder: '$(build.artifactstagingdirectory)/android'
      flattenFolders: true

  - task: Bash@3
    displayName: 'Duplicate com.mgflasher.app-Signed.apk'
    inputs:
      targetType: 'inline'
      script: |
        version=$(grep '<ApplicationDisplayVersion>' "$(Build.SourcesDirectory)/MgFlasher/MgFlasher.csproj" | sed -E 's/.*<ApplicationDisplayVersion>([^<]+)<.*/\1/')
        buildId="$(Build.BuildId)"
        cd "$(Build.ArtifactStagingDirectory)/android"
        if [ -f "com.mgflasher.app-Signed.apk" ]; then
          cp "com.mgflasher.app-Signed.apk" "com.mgflasher.app-Signed-v${version}b${buildId}.apk"
        fi

  # Publish all artifacts
  - task: PublishBuildArtifacts@1
    displayName: 'Publish All Artifacts'
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)'
      ArtifactName: 'drop_maui_all'
      publishLocation: 'Container'