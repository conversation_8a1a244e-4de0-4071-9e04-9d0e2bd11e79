# Overview

DFlasher is a user-friendly application designed for vehicle owners and enthusiasts to flash and diagnose ECUs (Engine Control Units) and TCUs (Transmission Control Units). 
It provides an intuitive interface to perform various operations such as:
�	Writing to ECUs/TCUs
�	Reading and clearing DTCs
�	Retrieving module information
Whether you are a professional mechanic or a car enthusiast, DFlasher simplifies the process of managing your vehicle's electronic control units.

# Changelog

All notable changes to this project will be documented in this file.

The format is based on [keep a changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [semantic versioning](https://semver.org/spec/v2.0.0.html).

## [43]

## Added
- Release Candidate