# Overview

VAG Flasher is a user-friendly application designed for vehicle owners and enthusiasts to flash and diagnose ECUs (Engine Control Units) in VAG (Volkswagen Auto Group) vehicles. 
It provides an intuitive interface to perform various operations such as:
�	Writing to ECUs/TCUs
�	Reading and clearing DTCs (Diagnostic Trouble Codes)
�	Retrieving module information
Whether you are a professional mechanic or a car enthusiast, VAG Flasher simplifies the process of managing your vehicle's electronic control units

# Changelog

All notable changes to this project will be documented in this file.

The format is based on [keep a changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [semantic versioning](https://semver.org/spec/v2.0.0.html).

## [31]

## Added
- Drastically improved method speeds and cut out unnecessary methods

## [29]

## Added
- PD100 - v3 OTA firmware

## [27]

## Changed
- Connection Stability Issues Addressed - Forgot to add back "nothing to update" flow

## [26]

## Changed
- Connection Stability Issues Addressed

## [25]

## Changed
- Revert last discovery devices adjustments

## [24]

## Changed
- PD ECU operations logs contract adjustment
- Discovering for devices adjustments

## [23]

## Added
- Report ECU operations to PD

## [22]

## Changed
- Stabilize OTA update process

## [21]

## Changed
- Adjusted obfuscation data

## [20]

## Added
- UpdateAdapterFirmware method to update the firmware of the adapter to the latest version automatically

## [19]

## Added
- Retrieve BT dongle version

## [18]

## Added
- Retrieve FZT from ecu

## [17]

## Changed
- Fixed connection drop during initial device detection

## [15]

## Added 
- Diagnostic Over Ip logger basic running.

## [14]

## Added 
- Fixed connection bugs with adapter, fixed CAN flash bug reported by PD.

## [13]

## Added 
- OTA update feature

## [12]

## Added
- Logger files upload process to logs cloud

## Changed
- Dev measurements presentation

## [11]

## Changed
- When Logger is started show only enabled logging parameters

## [10]

## Added
- Logger - logging to file
- Logger - select parameters to log

## [9]

## Added
- Colored log console
- Log ecu communication to separate file

## Changed
- Bluetooth connection will be reused across multiple commands

## [7]

## Changed
- Updated logger to display readable values 
- Logger - simple view

## [6]

## Changed
- Updated BT connection library to latest version

## [5]

## Added
- Added method to grab logger data from json

## [4]

## Added
- Only flashing option available using CODX files

## [3]

## Added
- Embedded Resource for basic JSON structure of logger variables
- Code to parse the JSON structure
- Code to pull DID array out for logger 


## [2]

## Added
- Versioning Flow

## [1]

## Changed
- Initial version of app